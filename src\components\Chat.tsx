import React, { useState, useEffect, useRef } from 'react';
import { Message } from '../types';
import { getChannelMessages, sendMessage, subscribeToChannelMessages } from '../services/messageService';
import { getChannel } from '../services/channelService';
import { useAuth } from '../context/AuthContext';

interface ChatProps {
  channelId: string;
}

const Chat: React.FC<ChatProps> = ({ channelId }) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [messageInput, setMessageInput] = useState('');
  const [channel, setChannel] = useState<any>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const { currentUser } = useAuth();

  // Fetch channel info and messages
  useEffect(() => {
    const fetchChannelAndMessages = async () => {
      setLoading(true);
      setError('');
      
      try {
        // Get channel info
        const channelData = await getChannel(channelId);
        if (channelData) {
          setChannel(channelData);
        }
        
        // Get messages
        const messagesData = await getChannelMessages(channelId);
        setMessages(messagesData);
      } catch (err) {
        console.error('Error fetching channel data:', err);
        setError('Failed to load channel data. Please try again.');
      } finally {
        setLoading(false);
      }
    };
    
    fetchChannelAndMessages();
  }, [channelId]);

  // Subscribe to new messages
  useEffect(() => {
    const unsubscribe = subscribeToChannelMessages(channelId, (newMessage) => {
      setMessages(prevMessages => [...prevMessages, newMessage]);
    });
    
    return () => {
      unsubscribe();
    };
  }, [channelId]);

  // Scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Handle sending a message
  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!messageInput.trim() || !currentUser) return;
    
    try {
      await sendMessage(channelId, messageInput);
      setMessageInput('');
    } catch (err) {
      console.error('Error sending message:', err);
      setError('Failed to send message. Please try again.');
    }
  };

  // Format timestamp
  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  // Format date for message groups
  const formatMessageDate = (timestamp: string) => {
    const date = new Date(timestamp);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    
    if (date.toDateString() === today.toDateString()) {
      return 'Today';
    } else if (date.toDateString() === yesterday.toDateString()) {
      return 'Yesterday';
    } else {
      return date.toLocaleDateString(undefined, { 
        month: 'long', 
        day: 'numeric',
        year: date.getFullYear() !== today.getFullYear() ? 'numeric' : undefined
      });
    }
  };

  // Group messages by author and date
  const groupedMessages = messages.reduce((groups: any[], message, index) => {
    const previousMessage = messages[index - 1];
    
    // Check if this is a new day compared to the previous message
    if (index === 0 || formatMessageDate(message.timestamp) !== formatMessageDate(previousMessage.timestamp)) {
      groups.push({
        type: 'date-divider',
        date: formatMessageDate(message.timestamp),
        id: `date-${message.timestamp}`
      });
    }
    
    // Check if this message should be grouped with the previous one
    const shouldGroup = 
      previousMessage && 
      message.author.id === previousMessage.author.id &&
      new Date(message.timestamp).getTime() - new Date(previousMessage.timestamp).getTime() < 5 * 60 * 1000; // 5 minutes
    
    if (shouldGroup) {
      // Add to the previous group
      const lastGroup = groups[groups.length - 1];
      if (lastGroup.type === 'message-group') {
        lastGroup.messages.push(message);
      } else {
        // Create a new group
        groups.push({
          type: 'message-group',
          author: message.author,
          messages: [message],
          id: `group-${message.id}`
        });
      }
    } else {
      // Create a new group
      groups.push({
        type: 'message-group',
        author: message.author,
        messages: [message],
        id: `group-${message.id}`
      });
    }
    
    return groups;
  }, []);

  if (loading) {
    return (
      <div className="flex-1 flex items-center justify-center bg-discord-bg">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-discord-primary"></div>
      </div>
    );
  }

  return (
    <div className="flex-1 flex flex-col bg-discord-bg">
      {/* Channel header */}
      <div className="p-4 shadow-md bg-discord-light-bg flex items-center">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-5 h-5 mr-2 text-discord-muted">
          <path fillRule="evenodd" d="M4.848 2.771A49.144 49.144 0 0112 2.25c2.43 0 4.817.178 7.152.52 1.978.292 3.348 2.024 3.348 3.97v6.02c0 1.946-1.37 3.678-3.348 3.97a48.901 48.901 0 01-3.476.383.39.39 0 00-.297.17l-2.755 4.133a.75.75 0 01-1.248 0l-2.755-4.133a.39.39 0 00-.297-.17 48.9 48.9 0 01-3.476-.384c-1.978-.29-3.348-2.024-3.348-3.97V6.741c0-1.946 1.37-3.68 3.348-3.97zM6.75 8.25a.75.75 0 01.75-.75h9a.75.75 0 010 1.5h-9a.75.75 0 01-.75-.75zm.75 2.25a.75.75 0 000 1.5H12a.75.75 0 000-1.5H7.5z" clipRule="evenodd" />
        </svg>
        <h2 className="font-bold text-white">{channel?.name || 'Loading...'}</h2>
        {channel?.topic && (
          <>
            <div className="mx-2 text-discord-muted">|</div>
            <p className="text-sm text-discord-muted">{channel.topic}</p>
          </>
        )}
      </div>

      {/* Messages area */}
      <div className="flex-1 overflow-y-auto p-4">
        {error && (
          <div className="p-4 mb-4 text-white bg-discord-red rounded-md">
            {error}
          </div>
        )}

        {groupedMessages.length === 0 && !loading && (
          <div className="flex flex-col items-center justify-center h-full text-discord-muted">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-16 h-16 mb-4">
              <path fillRule="evenodd" d="M4.848 2.771A49.144 49.144 0 0112 2.25c2.43 0 4.817.178 7.152.52 1.978.292 3.348 2.024 3.348 3.97v6.02c0 1.946-1.37 3.678-3.348 3.97a48.901 48.901 0 01-3.476.383.39.39 0 00-.297.17l-2.755 4.133a.75.75 0 01-1.248 0l-2.755-4.133a.39.39 0 00-.297-.17 48.9 48.9 0 01-3.476-.384c-1.978-.29-3.348-2.024-3.348-3.97V6.741c0-1.946 1.37-3.68 3.348-3.97zM6.75 8.25a.75.75 0 01.75-.75h9a.75.75 0 010 1.5h-9a.75.75 0 01-.75-.75zm.75 2.25a.75.75 0 000 1.5H12a.75.75 0 000-1.5H7.5z" clipRule="evenodd" />
            </svg>
            <p className="text-xl font-medium">No messages yet</p>
            <p className="mt-2">Be the first to send a message in #{channel?.name || 'this channel'}!</p>
          </div>
        )}

        {groupedMessages.map((group) => {
          if (group.type === 'date-divider') {
            return (
              <div key={group.id} className="flex items-center my-4">
                <div className="flex-1 h-px bg-discord-separator"></div>
                <div className="px-4 text-xs font-semibold text-discord-muted">{group.date}</div>
                <div className="flex-1 h-px bg-discord-separator"></div>
              </div>
            );
          }
          
          return (
            <div key={group.id} className="mb-4 group hover:bg-discord-light-bg -mx-2 p-2 rounded">
              <div className="flex">
                <div className="w-10 h-10 rounded-full bg-discord-primary flex items-center justify-center text-white mr-4">
                  {group.author.avatar}
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center">
                    <span className="font-medium text-white">{group.author.username}</span>
                    {group.author.isBot && (
                      <span className="ml-2 text-xs bg-discord-primary text-white px-1 rounded">BOT</span>
                    )}
                    <span className="ml-2 text-xs text-discord-muted">
                      {formatTimestamp(group.messages[0].timestamp)}
                    </span>
                  </div>
                  
                  {group.messages.map((message: Message) => (
                    <div key={message.id} className="mt-1 text-discord-text">
                      {message.content}
                      {message.editedTimestamp && (
                        <span className="text-xs text-discord-muted ml-1">(edited)</span>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          );
        })}
        
        <div ref={messagesEndRef} />
      </div>

      {/* Message input */}
      <div className="p-4 bg-discord-bg">
        <form onSubmit={handleSendMessage} className="bg-discord-input-bg rounded-lg p-2">
          <input
            type="text"
            placeholder={`Message #${channel?.name || 'channel'}`}
            className="w-full bg-transparent text-discord-text focus:outline-none"
            value={messageInput}
            onChange={(e) => setMessageInput(e.target.value)}
          />
        </form>
      </div>
    </div>
  );
};

export default Chat;
