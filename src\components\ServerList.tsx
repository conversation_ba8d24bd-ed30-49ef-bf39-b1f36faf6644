import React, { useState, useEffect } from 'react';
import { Link, useParams } from 'react-router-dom';
import { getUserServers, createServer } from '../services/serverService';
import { Server } from '../types';

const ServerList: React.FC = () => {
  const [servers, setServers] = useState<Server[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [isCreatingServer, setIsCreatingServer] = useState(false);
  const [newServerName, setNewServerName] = useState('');
  const { serverId } = useParams<{ serverId: string }>();

  useEffect(() => {
    const fetchServers = async () => {
      try {
        const userServers = await getUserServers();
        setServers(userServers);
      } catch (err) {
        console.error('Error fetching servers:', err);
        setError('Failed to load servers');
      } finally {
        setLoading(false);
      }
    };

    fetchServers();
  }, []);

  const handleCreateServer = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!newServerName.trim()) return;
    
    try {
      setLoading(true);
      const newServer = await createServer(newServerName);
      
      if (newServer) {
        setServers(prev => [...prev, newServer]);
        setNewServerName('');
        setIsCreatingServer(false);
      }
    } catch (err) {
      console.error('Error creating server:', err);
      setError('Failed to create server');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="w-18 bg-discord-dark-bg flex flex-col items-center py-3 overflow-y-auto">
      {/* Home button */}
      <Link
        to="/"
        className={`w-12 h-12 rounded-full bg-discord-light-bg flex items-center justify-center text-white mb-2 transition-all hover:rounded-2xl ${
          !serverId ? 'rounded-2xl bg-discord-primary' : ''
        }`}
      >
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-7 h-7">
          <path d="M11.47 3.84a.75.75 0 011.06 0l8.69 8.69a.75.75 0 101.06-1.06l-8.689-8.69a2.25 2.25 0 00-3.182 0l-8.69 8.69a.75.75 0 001.061 1.06l8.69-8.69z" />
          <path d="M12 5.432l8.159 8.159c.03.03.06.058.091.086v6.198c0 1.035-.84 1.875-1.875 1.875H15a.75.75 0 01-.75-.75v-4.5a.75.75 0 00-.75-.75h-3a.75.75 0 00-.75.75V21a.75.75 0 01-.75.75H5.625a1.875 1.875 0 01-1.875-1.875v-6.198a2.29 2.29 0 00.091-.086L12 5.43z" />
        </svg>
      </Link>

      {/* Server list separator */}
      <div className="w-8 h-0.5 bg-discord-separator rounded-full my-2"></div>

      {/* Server list */}
      <div className="flex flex-col items-center space-y-2 w-full">
        {loading && !isCreatingServer ? (
          <div className="w-12 h-12 rounded-full bg-discord-light-bg flex items-center justify-center">
            <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-discord-primary"></div>
          </div>
        ) : (
          <>
            {servers.map(server => (
              <Link
                key={server.id}
                to={`/servers/${server.id}`}
                className={`w-12 h-12 rounded-full bg-discord-light-bg flex items-center justify-center text-white transition-all hover:rounded-2xl ${
                  serverId === server.id ? 'rounded-2xl bg-discord-primary' : ''
                }`}
              >
                {server.icon ? (
                  <img
                    src={server.icon}
                    alt={server.name}
                    className="w-full h-full object-cover rounded-inherit"
                  />
                ) : (
                  <span className="text-xl font-medium">{server.name.charAt(0)}</span>
                )}
              </Link>
            ))}

            {/* Add server button */}
            <button
              onClick={() => setIsCreatingServer(true)}
              className="w-12 h-12 rounded-full bg-discord-light-bg flex items-center justify-center text-discord-green hover:bg-discord-green hover:text-white transition-all hover:rounded-2xl"
            >
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-7 h-7">
                <path fillRule="evenodd" d="M12 3.75a.75.75 0 01.75.75v6.75h6.75a.75.75 0 010 1.5h-6.75v6.75a.75.75 0 01-1.5 0v-6.75H4.5a.75.75 0 010-1.5h6.75V4.5a.75.75 0 01.75-.75z" clipRule="evenodd" />
              </svg>
            </button>
          </>
        )}
      </div>

      {/* Create server modal */}
      {isCreatingServer && (
        <div className="fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center z-50">
          <div className="bg-discord-light-bg rounded-lg w-full max-w-md p-6">
            <h2 className="text-xl font-bold text-white mb-4">Create a Server</h2>
            
            {error && (
              <div className="p-3 mb-4 text-white bg-discord-red rounded-md">
                {error}
              </div>
            )}
            
            <form onSubmit={handleCreateServer}>
              <div className="mb-4">
                <label htmlFor="serverName" className="block text-sm font-medium text-discord-channel mb-1">
                  SERVER NAME
                </label>
                <input
                  id="serverName"
                  type="text"
                  value={newServerName}
                  onChange={(e) => setNewServerName(e.target.value)}
                  placeholder="Enter a server name"
                  className="w-full px-3 py-2 text-white bg-discord-dark-bg border border-discord-separator rounded-md focus:outline-none focus:ring-2 focus:ring-discord-primary"
                  required
                />
              </div>
              
              <div className="flex justify-end">
                <button
                  type="button"
                  onClick={() => setIsCreatingServer(false)}
                  className="px-4 py-2 text-white bg-discord-dark-bg rounded-md mr-2 hover:bg-opacity-80"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={loading}
                  className="px-4 py-2 text-white bg-discord-primary rounded-md hover:bg-opacity-80 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-discord-primary disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {loading ? 'Creating...' : 'Create Server'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default ServerList;
