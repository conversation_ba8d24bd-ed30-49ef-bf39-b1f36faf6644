import React, { useState } from 'react';
import { useAuth } from '../context/AuthContext';
import UserSettings from './UserSettings';

const UserPanel: React.FC = () => {
  const { currentUser, logout } = useAuth();
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);

  if (!currentUser) return null;

  const getStatusColor = () => {
    switch (currentUser.status) {
      case 'online':
        return 'bg-discord-green';
      case 'idle':
        return 'bg-discord-yellow';
      case 'dnd':
        return 'bg-discord-red';
      case 'offline':
        return 'bg-discord-muted';
      default:
        return 'bg-discord-green';
    }
  };

  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error('Error logging out:', error);
    }
  };

  return (
    <>
      <div className="bg-discord-dark-bg p-2 flex items-center justify-between">
        <div className="flex items-center">
          <div className="relative mr-2">
            <div className="w-8 h-8 rounded-full bg-discord-primary flex items-center justify-center text-white">
              {currentUser.avatar}
            </div>
            <div className={`absolute bottom-0 right-0 w-3 h-3 rounded-full border-2 border-discord-dark-bg ${getStatusColor()}`}></div>
          </div>
          <div>
            <div className="text-white text-sm font-medium">{currentUser.username}</div>
            <div className="text-discord-muted text-xs">#{currentUser.discriminator}</div>
          </div>
        </div>
        <div className="flex">
          <button
            onClick={() => setIsSettingsOpen(true)}
            className="w-8 h-8 rounded-md flex items-center justify-center text-discord-muted hover:text-white hover:bg-discord-light-bg"
            title="User Settings"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
            </svg>
          </button>
          <button
            onClick={handleLogout}
            className="w-8 h-8 rounded-md flex items-center justify-center text-discord-muted hover:text-white hover:bg-discord-light-bg ml-1"
            title="Logout"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M3 3a1 1 0 00-1 1v12a1 1 0 001 1h12a1 1 0 001-1V7.414l-5.293-5.293A1 1 0 0010 2H3zm9 2.414L15.586 9H12V5.414zM4 4v12h12V10h-4a1 1 0 01-1-1V5H4z" clipRule="evenodd" />
              <path d="M7 11.5a.5.5 0 01.5-.5h5a.5.5 0 010 1h-5a.5.5 0 01-.5-.5z" />
            </svg>
          </button>
        </div>
      </div>

      <UserSettings isOpen={isSettingsOpen} onClose={() => setIsSettingsOpen(false)} />
    </>
  );
};

export default UserPanel;
