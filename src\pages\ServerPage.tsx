import React, { useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import ServerList from '../components/ServerList';
import ChannelList from '../components/ChannelList';
import Chat from '../components/Chat';
import MemberList from '../components/MemberList';
import { useAuth } from '../context/AuthContext';

const ServerPage: React.FC = () => {
  const { serverId, channelId } = useParams<{ serverId: string; channelId: string }>();
  const { currentUser } = useAuth();
  const navigate = useNavigate();

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!currentUser) {
      navigate('/login');
    }
  }, [currentUser, navigate]);

  // Redirect to first channel if no channel is selected
  useEffect(() => {
    if (serverId && !channelId) {
      // This would ideally fetch the first channel from the server
      // For now, we'll just redirect to a placeholder URL
      navigate(`/servers/${serverId}/channels/placeholder`);
    }
  }, [serverId, channelId, navigate]);

  if (!currentUser) {
    return null; // Will redirect to login
  }

  return (
    <div className="flex h-screen bg-discord-bg text-discord-text">
      <ServerList />
      <ChannelList />
      
      {channelId ? (
        <Chat channelId={channelId} />
      ) : (
        <div className="flex-1 flex items-center justify-center bg-discord-bg">
          <p className="text-discord-muted">Select a channel</p>
        </div>
      )}
      
      <MemberList serverId={serverId || ''} />
    </div>
  );
};

export default ServerPage;
