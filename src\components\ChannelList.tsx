import React, { useState, useEffect } from 'react';
import { Link, useParams } from 'react-router-dom';
import { getServerChannels, createChannel } from '../services/channelService';
import { getServer } from '../services/serverService';
import { Channel, Server } from '../types';

const ChannelList: React.FC = () => {
  const [channels, setChannels] = useState<Channel[]>([]);
  const [server, setServer] = useState<Server | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [isCreatingChannel, setIsCreatingChannel] = useState(false);
  const [newChannelName, setNewChannelName] = useState('');
  const [newChannelTopic, setNewChannelTopic] = useState('');
  const { serverId, channelId } = useParams<{ serverId: string; channelId: string }>();

  useEffect(() => {
    const fetchServerAndChannels = async () => {
      if (!serverId) return;
      
      try {
        setLoading(true);
        
        // Fetch server details
        const serverData = await getServer(serverId);
        if (serverData) {
          setServer(serverData);
        }
        
        // Fetch channels
        const channelsData = await getServerChannels(serverId);
        setChannels(channelsData);
      } catch (err) {
        console.error('Error fetching server data:', err);
        setError('Failed to load server data');
      } finally {
        setLoading(false);
      }
    };

    fetchServerAndChannels();
  }, [serverId]);

  const handleCreateChannel = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!newChannelName.trim() || !serverId) return;
    
    try {
      setLoading(true);
      const newChannel = await createChannel(
        serverId,
        newChannelName.toLowerCase().replace(/\s+/g, '-'),
        'text',
        { topic: newChannelTopic }
      );
      
      if (newChannel) {
        setChannels(prev => [...prev, newChannel]);
        setNewChannelName('');
        setNewChannelTopic('');
        setIsCreatingChannel(false);
      }
    } catch (err) {
      console.error('Error creating channel:', err);
      setError('Failed to create channel');
    } finally {
      setLoading(false);
    }
  };

  // Group channels by type
  const textChannels = channels.filter(channel => channel.type === 'text');
  const voiceChannels = channels.filter(channel => channel.type === 'voice');
  const announcementChannels = channels.filter(channel => channel.type === 'announcement');

  return (
    <div className="w-60 bg-discord-light-bg flex flex-col">
      {/* Server header */}
      <div className="p-4 shadow-md flex items-center justify-between">
        <h2 className="font-bold text-white truncate">
          {loading ? 'Loading...' : server?.name || 'Server'}
        </h2>
        <button className="text-discord-muted hover:text-white">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-5 h-5">
            <path fillRule="evenodd" d="M12.53 16.28a.75.75 0 01-1.06 0l-7.5-7.5a.75.75 0 011.06-1.06L12 14.69l6.97-6.97a.75.75 0 111.06 1.06l-7.5 7.5z" clipRule="evenodd" />
          </svg>
        </button>
      </div>

      {/* Channel list */}
      <div className="flex-1 overflow-y-auto p-2">
        {error && (
          <div className="p-3 mb-4 text-white bg-discord-red rounded-md text-sm">
            {error}
          </div>
        )}

        {loading ? (
          <div className="flex items-center justify-center h-20">
            <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-discord-primary"></div>
          </div>
        ) : (
          <>
            {/* Text channels */}
            <div className="mb-4">
              <div className="flex items-center justify-between px-1 mb-1">
                <h3 className="text-xs font-semibold text-discord-muted uppercase tracking-wider">
                  Text Channels
                </h3>
                <button
                  onClick={() => setIsCreatingChannel(true)}
                  className="text-discord-muted hover:text-white"
                  title="Create Channel"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-4 h-4">
                    <path fillRule="evenodd" d="M12 3.75a.75.75 0 01.75.75v6.75h6.75a.75.75 0 010 1.5h-6.75v6.75a.75.75 0 01-1.5 0v-6.75H4.5a.75.75 0 010-1.5h6.75V4.5a.75.75 0 01.75-.75z" clipRule="evenodd" />
                  </svg>
                </button>
              </div>
              
              {textChannels.length === 0 ? (
                <p className="text-discord-muted text-sm px-2">No text channels</p>
              ) : (
                <div className="space-y-0.5">
                  {textChannels.map(channel => (
                    <Link
                      key={channel.id}
                      to={`/servers/${serverId}/channels/${channel.id}`}
                      className={`flex items-center px-2 py-1 rounded group hover:bg-discord-bg ${
                        channelId === channel.id ? 'bg-discord-bg text-white' : 'text-discord-channel hover:text-discord-text'
                      }`}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-5 h-5 mr-1 text-discord-muted">
                        <path fillRule="evenodd" d="M4.848 2.771A49.144 49.144 0 0112 2.25c2.43 0 4.817.178 7.152.52 1.978.292 3.348 2.024 3.348 3.97v6.02c0 1.946-1.37 3.678-3.348 3.97a48.901 48.901 0 01-3.476.383.39.39 0 00-.297.17l-2.755 4.133a.75.75 0 01-1.248 0l-2.755-4.133a.39.39 0 00-.297-.17 48.9 48.9 0 01-3.476-.384c-1.978-.29-3.348-2.024-3.348-3.97V6.741c0-1.946 1.37-3.68 3.348-3.97zM6.75 8.25a.75.75 0 01.75-.75h9a.75.75 0 010 1.5h-9a.75.75 0 01-.75-.75zm.75 2.25a.75.75 0 000 1.5H12a.75.75 0 000-1.5H7.5z" clipRule="evenodd" />
                      </svg>
                      <span className="truncate">{channel.name}</span>
                    </Link>
                  ))}
                </div>
              )}
            </div>

            {/* Voice channels */}
            {voiceChannels.length > 0 && (
              <div className="mb-4">
                <h3 className="text-xs font-semibold text-discord-muted uppercase tracking-wider px-1 mb-1">
                  Voice Channels
                </h3>
                <div className="space-y-0.5">
                  {voiceChannels.map(channel => (
                    <div
                      key={channel.id}
                      className="flex items-center px-2 py-1 rounded text-discord-channel hover:text-discord-text hover:bg-discord-bg"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-5 h-5 mr-1 text-discord-muted">
                        <path d="M8.25 4.5a3.75 3.75 0 117.5 0v8.25a3.75 3.75 0 11-7.5 0V4.5z" />
                        <path d="M6 10.5a.75.75 0 01.75.75v1.5a5.25 5.25 0 1010.5 0v-1.5a.75.75 0 011.5 0v1.5a6.751 6.751 0 01-6 6.709v2.291h3a.75.75 0 010 1.5h-7.5a.75.75 0 010-1.5h3v-2.291a6.751 6.751 0 01-6-6.709v-1.5A.75.75 0 016 10.5z" />
                      </svg>
                      <span className="truncate">{channel.name}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Announcement channels */}
            {announcementChannels.length > 0 && (
              <div className="mb-4">
                <h3 className="text-xs font-semibold text-discord-muted uppercase tracking-wider px-1 mb-1">
                  Announcements
                </h3>
                <div className="space-y-0.5">
                  {announcementChannels.map(channel => (
                    <Link
                      key={channel.id}
                      to={`/servers/${serverId}/channels/${channel.id}`}
                      className={`flex items-center px-2 py-1 rounded group hover:bg-discord-bg ${
                        channelId === channel.id ? 'bg-discord-bg text-white' : 'text-discord-channel hover:text-discord-text'
                      }`}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-5 h-5 mr-1 text-discord-muted">
                        <path fillRule="evenodd" d="M4.125 3C3.089 3 2.25 3.84 2.25 4.875V18a3 3 0 003 3h15a3 3 0 01-3-3V4.875C17.25 3.839 16.41 3 15.375 3H4.125zM12 9.75a.75.75 0 000 1.5h1.5a.75.75 0 000-1.5H12zm-.75-2.25a.75.75 0 01.75-.75h1.5a.75.75 0 010 1.5H12a.75.75 0 01-.75-.75zM6 12.75a.75.75 0 000 1.5h7.5a.75.75 0 000-1.5H6zm-.75 3.75a.75.75 0 01.75-.75h7.5a.75.75 0 010 1.5H6a.75.75 0 01-.75-.75zM6 6.75a.75.75 0 00-.75.75v.75c0 .414.336.75.75.75h3a.75.75 0 00.75-.75v-.75a.75.75 0 00-.75-.75H6z" clipRule="evenodd" />
                        <path d="M18.75 6.75h1.875c.621 0 1.125.504 1.125 1.125V18a1.5 1.5 0 01-3 0V6.75z" />
                      </svg>
                      <span className="truncate">{channel.name}</span>
                    </Link>
                  ))}
                </div>
              </div>
            )}
          </>
        )}
      </div>

      {/* Create channel modal */}
      {isCreatingChannel && (
        <div className="fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center z-50">
          <div className="bg-discord-light-bg rounded-lg w-full max-w-md p-6">
            <h2 className="text-xl font-bold text-white mb-4">Create Text Channel</h2>
            
            {error && (
              <div className="p-3 mb-4 text-white bg-discord-red rounded-md">
                {error}
              </div>
            )}
            
            <form onSubmit={handleCreateChannel}>
              <div className="mb-4">
                <label htmlFor="channelName" className="block text-sm font-medium text-discord-channel mb-1">
                  CHANNEL NAME
                </label>
                <div className="flex items-center px-3 py-2 bg-discord-dark-bg border border-discord-separator rounded-md">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-5 h-5 mr-2 text-discord-muted">
                    <path fillRule="evenodd" d="M4.848 2.771A49.144 49.144 0 0112 2.25c2.43 0 4.817.178 7.152.52 1.978.292 3.348 2.024 3.348 3.97v6.02c0 1.946-1.37 3.678-3.348 3.97a48.901 48.901 0 01-3.476.383.39.39 0 00-.297.17l-2.755 4.133a.75.75 0 01-1.248 0l-2.755-4.133a.39.39 0 00-.297-.17 48.9 48.9 0 01-3.476-.384c-1.978-.29-3.348-2.024-3.348-3.97V6.741c0-1.946 1.37-3.68 3.348-3.97zM6.75 8.25a.75.75 0 01.75-.75h9a.75.75 0 010 1.5h-9a.75.75 0 01-.75-.75zm.75 2.25a.75.75 0 000 1.5H12a.75.75 0 000-1.5H7.5z" clipRule="evenodd" />
                  </svg>
                  <input
                    id="channelName"
                    type="text"
                    value={newChannelName}
                    onChange={(e) => setNewChannelName(e.target.value)}
                    placeholder="new-channel"
                    className="w-full bg-transparent text-white focus:outline-none"
                    required
                  />
                </div>
                <p className="text-xs text-discord-muted mt-1">
                  Use lowercase letters, numbers, hyphens, and underscores
                </p>
              </div>
              
              <div className="mb-6">
                <label htmlFor="channelTopic" className="block text-sm font-medium text-discord-channel mb-1">
                  CHANNEL TOPIC (OPTIONAL)
                </label>
                <input
                  id="channelTopic"
                  type="text"
                  value={newChannelTopic}
                  onChange={(e) => setNewChannelTopic(e.target.value)}
                  placeholder="Enter a topic"
                  className="w-full px-3 py-2 text-white bg-discord-dark-bg border border-discord-separator rounded-md focus:outline-none focus:ring-2 focus:ring-discord-primary"
                />
              </div>
              
              <div className="flex justify-end">
                <button
                  type="button"
                  onClick={() => setIsCreatingChannel(false)}
                  className="px-4 py-2 text-white bg-discord-dark-bg rounded-md mr-2 hover:bg-opacity-80"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={loading}
                  className="px-4 py-2 text-white bg-discord-primary rounded-md hover:bg-opacity-80 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-discord-primary disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {loading ? 'Creating...' : 'Create Channel'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default ChannelList;
